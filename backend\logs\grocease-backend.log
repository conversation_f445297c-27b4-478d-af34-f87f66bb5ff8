2025-07-10 08:23:50.165 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 21.0.7 with PID 14048 (C:\Users\<USER>\Desktop\ashish\grocease\backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\grocease\backend)
2025-07-10 08:23:50.178 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-10 08:23:51.530 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10 08:23:51.640 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98 ms. Found 11 JPA repository interfaces.
2025-07-10 08:23:52.786 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-10 08:23:52.809 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 08:23:52.810 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-10 08:23:52.933 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-10 08:23:52.934 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2687 ms
2025-07-10 08:23:53.401 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10 08:23:53.491 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-07-10 08:23:53.550 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-10 08:23:53.923 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-10 08:23:53.963 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-10 08:23:54.233 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@21ffc00e
2025-07-10 08:23:54.234 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-10 08:23:55.726 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-10 08:23:56.003 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 08:23:56.388 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-10 08:23:57.282 [main] WARN  c.g.service.NotificationService - Firebase messaging is not available. Push notifications will be disabled.
2025-07-10 08:23:57.741 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used for username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider.
2025-07-10 08:23:57.963 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-10 08:23:58.720 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-10 08:23:59.358 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-10 08:23:59.380 [main] INFO  com.grocease.GrocEaseApplication - Started GrocEaseApplication in 9.837 seconds (process running for 10.563)
2025-07-10 08:24:06.648 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-10 08:24:06.846 [http-nio-8080-exec-2] INFO  c.grocease.controller.AuthController - Login attempt for email: <EMAIL>
2025-07-10 08:24:07.074 [http-nio-8080-exec-2] ERROR c.g.exception.GlobalExceptionHandler - Authentication failed: Bad credentials
