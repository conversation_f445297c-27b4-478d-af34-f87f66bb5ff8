#!/bin/bash

# Test script to verify user registration without email verification
# This script tests the registration endpoint to ensure email verification is disabled

echo "Testing GrocEase User Registration (Email Verification Disabled)"
echo "================================================================"

# Test data
EMAIL="test$(date +%s)@example.com"  # Unique email using timestamp
NAME="Test User"
PHONE="+1234567890$(date +%s | tail -c 4)"  # Unique phone
PASSWORD="testPassword123"

echo "Testing user registration..."
echo "Email: $EMAIL"
echo "Name: $NAME"
echo "Phone: $Phone"

# Register user
RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"$NAME\",
    \"email\": \"$EMAIL\",
    \"phone\": \"$PHONE\",
    \"password\": \"$PASSWORD\",
    \"confirmPassword\": \"$PASSWORD\"
  }")

echo "Registration Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"

# Extract token from response
TOKEN=$(echo "$RESPONSE" | jq -r '.data.token' 2>/dev/null)

if [ "$TOKEN" != "null" ] && [ "$TOKEN" != "" ]; then
    echo ""
    echo "✅ Registration successful! Token received."
    echo "✅ Email verification is disabled - user can register without email verification."
    
    # Test accessing protected endpoint
    echo ""
    echo "Testing access to protected endpoint with received token..."
    PROFILE_RESPONSE=$(curl -s -X GET http://localhost:8080/api/users/profile \
      -H "Authorization: Bearer $TOKEN")
    
    echo "Profile Response:"
    echo "$PROFILE_RESPONSE" | jq '.' 2>/dev/null || echo "$PROFILE_RESPONSE"
    
    # Check if email is verified in profile
    EMAIL_VERIFIED=$(echo "$PROFILE_RESPONSE" | jq -r '.data.isEmailVerified' 2>/dev/null)
    
    if [ "$EMAIL_VERIFIED" = "true" ]; then
        echo ""
        echo "✅ SUCCESS: Email verification is disabled!"
        echo "✅ User profile shows isEmailVerified: true by default"
        echo "✅ User can access protected endpoints immediately after registration"
    else
        echo ""
        echo "❌ ISSUE: Email verification might still be required"
        echo "❌ User profile shows isEmailVerified: $EMAIL_VERIFIED"
    fi
    
else
    echo ""
    echo "❌ Registration failed or no token received"
    echo "❌ Please check the application logs for errors"
fi

echo ""
echo "Test completed."
