# =============================================================================
# GROCEASE BACKEND - SINGLE PRODUCTION-READY CONFIGURATION
# =============================================================================
# Optimized for production with PostgreSQL
# All settings controlled via environment variables for flexibility
# =============================================================================

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api
  error:
    include-message: always
    include-binding-errors: always
  # Production optimizations
  compression:
    enabled: ${SERVER_COMPRESSION:true}
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
  http2:
    enabled: ${SERVER_HTTP2:true}

# Spring Configuration
spring:
  application:
    name: grocease-backend

  # Database Configuration - PostgreSQL optimized
  datasource:
    url: ${DATABASE_URL:********************************************}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:admin}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:20}
      minimum-idle: ${DB_MIN_IDLE:5}
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}

  # JPA Configuration - Production optimized
  jpa:
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:update}
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        format_sql: ${JPA_FORMAT_SQL:false}
        jdbc:
          batch_size: ${JPA_BATCH_SIZE:50}
        order_inserts: true
        order_updates: true
        generate_statistics: ${JPA_STATISTICS:false}
    defer-datasource-initialization: true

  # SQL Initialization
  sql:
    init:
      mode: ${SQL_INIT_MODE:never}
      data-locations: ${SQL_DATA_LOCATIONS:classpath:data.sql}

  # Mail Configuration
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:10MB}
      max-request-size: ${MAX_REQUEST_SIZE:10MB}

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:}
  expiration: ${JWT_EXPIRATION:********} # 24 hours in milliseconds
  refresh-expiration: ${JWT_REFRESH_EXPIRATION:*********} # 7 days in milliseconds

# Cloudinary Configuration
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:}
  api-key: ${CLOUDINARY_API_KEY:}
  api-secret: ${CLOUDINARY_API_SECRET:}

# Firebase Configuration
firebase:
  enabled: ${FIREBASE_ENABLED:false}
  config:
    file: ${FIREBASE_CONFIG_FILE:firebase-service-account.json}

# Application Configuration
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:19006,http://localhost:8081}
  security:
    jwt:
      header: ${JWT_HEADER:Authorization}
      prefix: ${JWT_PREFIX:Bearer }
  features:
    email-verification:
      enabled: ${EMAIL_VERIFICATION_ENABLED:false}  # Disabled for development

# Logging Configuration - Production optimized
logging:
  level:
    com.grocease: ${LOG_LEVEL_APP:INFO}
    org.springframework.security: ${LOG_LEVEL_SECURITY:WARN}
    org.hibernate.SQL: ${LOG_LEVEL_SQL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${LOG_LEVEL_SQL_PARAMS:WARN}
    org.springframework.web: ${LOG_LEVEL_WEB:WARN}
    org.springframework.boot: ${LOG_LEVEL_BOOT:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/grocease-backend.log}

# Management & Monitoring - Production secured
management:
  endpoints:
    web:
      exposure:
        include: ${MANAGEMENT_ENDPOINTS:health,info,metrics}
      base-path: /actuator
  endpoint:
    health:
      show-details: ${HEALTH_SHOW_DETAILS:when-authorized}
  health:
    mail:
      enabled: false  # Disable mail health check since mail is not configured
  info:
    env:
      enabled: ${MANAGEMENT_INFO_ENV:false}
