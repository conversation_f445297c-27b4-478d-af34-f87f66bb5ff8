# =============================================================================
# GROCEASE BACKEND - ENVIRONMENT VARIABLES TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Usage: cp .env.example .env
# Never commit .env file to version control!
# =============================================================================

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
SERVER_PORT=8080
SERVER_COMPRESSION=true
SERVER_HTTP2=true

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
# PostgreSQL Database URL (REQUIRED)
DATABASE_URL=********************************************

# Database credentials (REQUIRED)
DB_USERNAME=grocease_user
DB_PASSWORD=your_secure_password_here

# Database connection pool settings (optional - optimized defaults provided)
DB_POOL_SIZE=20
DB_MIN_IDLE=5
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000

# =============================================================================
# JPA/HIBERNATE CONFIGURATION
# =============================================================================
# DDL mode: validate (production), update (development)
JPA_DDL_AUTO=validate
JPA_SHOW_SQL=false
JPA_FORMAT_SQL=false
JPA_BATCH_SIZE=50
JPA_STATISTICS=false

# SQL initialization mode: never (production), always (if you have data.sql)
SQL_INIT_MODE=never

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED)
# =============================================================================
# JWT Secret Key (REQUIRED - must be at least 32 characters)
# Generate a strong secret: openssl rand -base64 32
JWT_SECRET=your_super_secret_jwt_key_at_least_32_characters_long_for_security
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# =============================================================================
# EMAIL CONFIGURATION (REQUIRED)
# =============================================================================
# SMTP Configuration (Required for OTP emails)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# =============================================================================
# CLOUDINARY CONFIGURATION (REQUIRED)
# =============================================================================
# Cloudinary settings (Required for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# =============================================================================
# FIREBASE CONFIGURATION (OPTIONAL)
# =============================================================================
# Enable/disable Firebase (set to true only if you have Firebase setup)
FIREBASE_ENABLED=false
# Firebase for Push Notifications
FIREBASE_CONFIG_FILE=firebase-service-account.json

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006,http://localhost:8081

# File Upload Settings
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=10MB

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging Levels (Optional - production defaults provided)
LOG_LEVEL_APP=INFO
LOG_LEVEL_SECURITY=WARN
LOG_LEVEL_SQL=WARN
LOG_LEVEL_SQL_PARAMS=WARN
LOG_LEVEL_WEB=WARN
LOG_LEVEL_BOOT=INFO

# Log file location
LOG_FILE=logs/grocease-backend.log

# =============================================================================
# MANAGEMENT & MONITORING
# =============================================================================
# Management endpoints (production secured)
MANAGEMENT_ENDPOINTS=health,info,metrics
HEALTH_SHOW_DETAILS=when-authorized
MANAGEMENT_INFO_ENV=false
LOG_LEVEL_SECURITY=DEBUG
LOG_LEVEL_SQL=DEBUG
LOG_LEVEL_SQL_PARAMS=TRACE
LOG_FILE=logs/grocease-backend.log

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Management Endpoints (Optional)
MANAGEMENT_ENDPOINTS=health,info,metrics
HEALTH_SHOW_DETAILS=when-authorized
